import React, { useEffect, useState } from "react";
import moment from 'moment';
import { REPORT_INCIDENT_URL_WITH_ID } from "../../constants";
import API from "../../services/API";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import { Modal, Button, Form } from 'react-bootstrap';
import PermitModal from "../PermitModal";
import { STATIC_URL } from "../../constants";

const Actions = ({ action, applicationType, setRendered }) => {

    console.log(action)
    console.log("testts")
    const [selectedAudit, setSelectedAudit] = useState('');
    const [actionModal, setActionModal] = useState(false);
    const currentDate = moment();
    const dueDateMoment = moment(action.dueDate, 'DD/MM/YYYY');
    const [reportData, setReportData] = useState([]);
    const isPastDue = dueDateMoment.isBefore(currentDate);
    const isToday = dueDateMoment.isSame(currentDate, 'day');
    const [actionOne, setActionOne] = useState([]);
    const [data, setData] = useState([]);
    const [requiredAction, setRequiredAction] = useState([]);
    const [showReportModal, setShowReportModal] = useState(false);
    const [auditModal, setAuditModal] = useState(false);
    const [auditFindingModal, setAuditFindingModal] = useState(false);
    const [applicationDetails, setApplicationDetails] = useState([]);
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.status': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
        description: { value: null, matchMode: FilterMatchMode.IN },
        dueDate: { value: null, matchMode: FilterMatchMode.IN },
        state: { value: null, matchMode: FilterMatchMode.IN },
    });

    useEffect(() => {
        if (action) {
            const required = action.map((item) => ({
                name: item.applicationDetails?.status,
                value: item.applicationDetails?.status,
            }));

            const uniqueRequiredActions = required.filter(
                (ele, ind) =>
                    ind === required.findIndex(
                        (elem) => elem.value === ele.value && elem.name === ele.name
                    )
            );

            console.log("Unique Required Actions: ", uniqueRequiredActions);
            setRequiredAction(uniqueRequiredActions);
        }
    }, [action]);


    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        setFilters((prevFilters) => ({
            ...prevFilters,
            global: { ...prevFilters.global, value },
        }));
    };

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-between align-items-center'>
                {applicationType === 'Observation' && (
                    <h5 className="m-0">
                        A listing of all actions due from you for the selected location(s) and time frame.
                    </h5>
                )}
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText
                        type="search"
                        value={value || ''}
                        onChange={onGlobalFilterChange}
                    />
                </span>
            </div>
        );
    };

    const header = renderHeader();

    const handleClick = (row) => {
        const uploads = row.applicationDetails.uploads?.map((i) => ({
            src: `${STATIC_URL}/${i}`,
            width: 4,
            height: 3,
        })) || [];

        row.applicationDetails.uploads = uploads;
        setApplicationDetails(row); // Ensure this contains necessary details
        setReportData(row.applicationDetails); // Verify that row.applicationDetails is correct

    };

    useEffect(() => {
        if (applicationDetails && reportData && Object.keys(applicationDetails).length > 0) {
            setShowReportModal(true);
        }
    }, [applicationDetails, reportData]);
    const requiredFilterTemplate = (options) => {
        return (
            <div>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect
                    value={options.value}
                    options={requiredAction}
                    onChange={(e) => options.filterCallback(e.value)}
                    optionLabel="name"
                    placeholder="Any"
                    className="p-column-filter"
                />
            </div>
        );
    };


    const representativesItemTemplate = (option) => (
        <div className="flex align-items-center gap-2">
            <span>{option.value}</span>
        </div>
    );

    const maskIdBodyTemplate = (row) => {
        const disabledStatuses = ['Pending HRA Approval', 'Pending Approval', 'Pending HRA Assessment'];
        const isDisabled = disabledStatuses.includes(row.applicationDetails.status);

        return (
            <div
                className={isDisabled ? '' : 'maskid'}
                onClick={isDisabled ? undefined : () => handleClick(row)}
                style={isDisabled ? { cursor: 'default' } : { cursor: 'pointer' }}
            >
                # {row.applicationDetails.maskId}
            </div>
        );
    };

    const requiredActionBody = (row) => {
        if (row.applicationDetails.status === 'Pending HRA Approval') {
            return 'Pending HRA Approval (Approve ePTW) from Mobile App'
        }else if(row.applicationDetails.status === 'Pending Approval'){
            return 'Pending Approval (Approve ePTW) from Mobile App'
        }else if(row.applicationDetails.status === 'Pending DCSO Isolation / Acknowledgement'){
            return 'Pending DCSO Isolation / Acknowledgement (Undertake DCSO Acknolwedgement / Isolation)'
        }else if(row.applicationDetails.status === 'Pending HRA Assessment'){
            return 'Pending HRA Assessment (Conduct HRA) from Mobile App'
        }else{
            return row.applicationDetails.status
        }

    };

    return (
        <>
            <DataTable
                value={action}
                paginator
                rows={10}
                header={header}
                filters={filters}
                onFilter={(e) => setFilters(e.filters)}
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found."
                tableStyle={{ minWidth: '50rem' }}
                sortField="applicationDetails.maskId"
                sortOrder={-1}
            >
                <Column
                    field="applicationDetails.maskId"
                    body={maskIdBodyTemplate}
                    header="ID"
                    sortable
                    style={{ width: '15%' }}
                />
                <Column
                    field="applicationDetails.status"
                    header="Required Action"
                    body={requiredActionBody}
                    filter
                    filterElement={requiredFilterTemplate}
                    showFilterMatchModes={false}
                    style={{ width: '15%' }}
                />
                <Column
                    field="applicationDetails.locationFour.name"
                    header="Project / Dc"
                    style={{ width: '15%' }}
                />
                <Column
                    field="dueDate"
                    header="Due Date"
                    style={{ width: '15%' }}
                />
            </DataTable>

            {showReportModal && (
                <PermitModal
                    applicationDetails={applicationDetails}
                    reportData={reportData}
                    showReportModal={showReportModal}
                    setShowReportModal={setShowReportModal}
                />
            )}
        </>
    );
};

export default Actions;
