import React, { useState, useEffect } from 'react';
import $ from "jquery";
import { <PERSON><PERSON>, Button } from 'react-bootstrap';
import { Button as PrimeButton } from 'primereact/button';

import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';

window.jQuery = $;
window.$ = $;

const MonthlyReport = ({ data, exportYearWiseExcelWithProjectName }) => {
    const [final, setFinal] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    // Get the year from the first item in the data array
    const [selectedYear, setSelectedYear] = useState(() => {
        if (data && data.length > 0 && data[0].year) {
            return data[0].year;
        }
        return new Date().getFullYear().toString();
    });
    const [showReportModal, setShowReportModal] = useState(false);
    const [selectedReport, setSelectedReport] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('');

    useEffect(() => {
        setFinal(data);
        setFilteredData(data);

        // Update selectedYear when data changes
        if (data && data.length > 0 && data[0].year) {
            setSelectedYear(data[0].year);
        }
    }, [data]);

    // Filter data when search term or status changes
    useEffect(() => {
        filterData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [searchTerm, selectedStatus, final]);

    const filterData = () => {
        if (!final || !final.length) return;

        let filtered = [...final];

        // Filter by location name
        if (searchTerm) {
            filtered = filtered.filter(location =>
                location.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by status if selected
        if (selectedStatus) {
            filtered = filtered.filter(location => {
                // Check if any month has the selected status
                return months.some(month => {
                    const status = getStatusForMonth(location, month);

                    if (selectedStatus === 'approved' && status.status === 'Approved') {
                        return true;
                    } else if (selectedStatus === 'submitted' && status.color === '#f8d568' && status.status !== 'No Submission') {
                        return true;
                    } else if (selectedStatus === 'no-submission-yellow' &&
                        status.status === 'No Submission' &&
                        status.color === '#f8d568') {
                        return true;
                    } else if (selectedStatus === 'no-submission-blue' &&
                        status.status === 'No Submission' &&
                        status.color === '#90caf9') {
                        return true;
                    } else if (selectedStatus === 'no-assigned' &&
                        status.status === 'No Assigned Reporter and/or Reviewer') {
                        return true;
                    }

                    return false;
                });
            });
        }

        setFilteredData(filtered);
    };



    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const getStatusForMonth = (location, month) => {
        // Find the data for this location and month
        if (!location.data) return { status: 'No Submission', color: '#f8d568' }; // Yellow for no submission

        // Check if there's a reporter for this month
        const reportedBy = location.data.find(item => item.name === 'Reported By');
        const reviewedBy = location.data.find(item => item.name === 'Reviewed By');

        if (reportedBy) {
            const reporter = reportedBy[month.toLowerCase()];
            const reviewer = reviewedBy ? reviewedBy[month.toLowerCase()] : '-';

            if (reporter && reporter !== '-') {
                if (reviewer && reviewer !== '-') {
                    return {
                        status: 'Approved',  // Changed to show "Approved" when both reporter and reviewer exist
                        color: '#4caf50',  // Green for submitted and reviewed
                        reporter: reporter,
                        reviewer: reviewer
                    };
                }
                return {
                    status: reporter,
                    color: '#f8d568',  // Yellow for submitted but not reviewed
                    reporter: reporter,
                    reviewer: '-'
                };
            }
        }

        // Check if the site has no submission for this month
        const date = location.data.find(item => item.name === 'date');
        if (date && date[month.toLowerCase()] === '-') {
            return { status: 'No Submission', color: '#90caf9' }; // Blue for no submission
        }

        return {
            status: 'No Assigned Reporter and/or Reviewer',
            color: '#ef5350',  // Red for no assignment
            reporter: '-',
            reviewer: '-'
        };
    };

    const handleViewReport = (location, month) => {
        // Find the data for this location and month
        const monthData = {};

        if (location.data) {
            location.data.forEach(item => {
                monthData[item.name] = item[month.toLowerCase()];
            });
        }

        setSelectedReport({
            location: location.name,
            month: month,
            year: selectedYear,
            data: monthData
        });

        setShowReportModal(true);
    };

    const renderMonthCell = (location, month) => {
        const status = getStatusForMonth(location, month);

        // Define gradient backgrounds based on status
        let backgroundStyle = {};

        if (status.status === 'Approved' && status.color === '#4caf50') {
            // Green for approved (when data has reviewer)
            backgroundStyle = {
                background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)'
            };
        } else if (status.status === 'No Submission' && status.color === '#f8d568') {
            // Yellow for no submission (original)
            backgroundStyle = {
                background: 'linear-gradient(135deg, #f8d568 0%, #f5c542 100%)'
            };
        } else if (status.status === 'No Submission' && status.color === '#90caf9') {
            // Blue for no submission (previously "Inactive Site")
            backgroundStyle = {
                background: 'linear-gradient(135deg, #90caf9 0%, #64b5f6 100%)'
            };
        } else if (status.status === 'No Assigned Reporter and/or Reviewer') {
            backgroundStyle = {
                background: 'linear-gradient(135deg, #ef5350 0%, #e53935 100%)'
            };
        } else {
            // Default yellow for submitted but not reviewed
            backgroundStyle = {
                background: 'linear-gradient(135deg, #f8d568 0%, #f5c542 100%)'
            };
        }

        return (
            <div
                key={`${location.name}-${month}`}
                className="month-cell"
                style={{
                    ...backgroundStyle,
                    padding: '10px',
                    textAlign: 'center',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    color: '#fff'
                }}
            >
                <div>{status.status}</div>
                {/* Show View button for all cells except those with blue "No Submission" status */}
                {!(status.status === 'No Submission' && status.color === '#90caf9') && (
                    <button
                        className="btn btn-sm btn-light view-btn"
                        onClick={() => handleViewReport(location, month)}
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            margin: '5px auto 0',
                            padding: '2px 8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            border: 'none'
                        }}
                    >
                        <i className="pi pi-eye" style={{ marginRight: '5px' }}></i> View
                    </button>
                )}
            </div>
        );
    };

    const ReportDetailModal = () => {
        if (!selectedReport) return null;

        // Group data into categories
        const gdcEmployees = {
            "# of STT GDC Employees": selectedReport.data.numberOfEmployees || '0',
            "Total Hours Worked": selectedReport.data.dailyHoursOfEmployee || '0',
            "Total Working Days": selectedReport.data.workingDaysOfEmployee || '0'
        };

        const contractors = {
            "# of Contractor Employees": selectedReport.data.numberofContractors || '0',
            "Daily Hours Worked": selectedReport.data.dailyHoursOfContractors || '0',
            "Total Working Days": selectedReport.data.workingDaysOfContractors || '0'
        };

        const safetyActivities = {
            "# of Safety Inductions": selectedReport.data.noOfSafety || '0',
            "# of Toolbox Meetings": selectedReport.data.noOfToolbox || '0'
        };

        const ehsActivities = {
            "# of EHS Trainings": selectedReport.data.noOfEhsTraining || '0',
            "# of EHS Inspections/Audits": selectedReport.data.noOfInspection || '0',
            "# of Site Walk/Inspection": selectedReport.data.noOfManagmentSiteWalk || '0'
        };

        const external = {
            "# of Authority/NGO/Union Visits": selectedReport.data.noOfAuthority || '0'
        };

        return (
            <Modal
                show={showReportModal}
                onHide={() => setShowReportModal(false)}
                size="lg"
                centered
                dialogClassName="report-modal"
            >
                <Modal.Header closeButton style={{ borderBottom: 'none', paddingBottom: '0' }}>
                    <div className="d-flex align-items-center w-100">
                        <div className="bg-primary text-white p-3 me-3 rounded" style={{ minWidth: '120px', textAlign: 'center' }}>
                            EHS Report
                        </div>
                        <div style={{ fontSize: '1.2rem', fontWeight: '500' }}>
                            {selectedReport.location} - {selectedReport.month} {selectedReport.year}
                        </div>
                    </div>
                </Modal.Header>
                <Modal.Body style={{ padding: '20px 30px' }}>
                    <div className="container-fluid p-0">
                        <div className="row g-3">
                            {/* GDC Employees Section */}
                            <div className="col-md-6 mb-3">
                                <div className="card h-100" style={{
                                    backgroundColor: '#e3f2fd',
                                    borderRadius: '8px',
                                    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                                    border: 'none'
                                }}>
                                    <div className="card-body">
                                        <h5 className="card-title" style={{ color: '#1565c0', marginBottom: '15px', fontWeight: '600' }}>GDC Employees</h5>
                                        {Object.entries(gdcEmployees).map(([key, value]) => (
                                            <div className="d-flex justify-content-between mb-2" key={key}>
                                                <span>{key}:</span>
                                                <strong>{value}</strong>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Contractors Section */}
                            <div className="col-md-6 mb-3">
                                <div className="card h-100" style={{
                                    backgroundColor: '#e8f5e9',
                                    borderRadius: '8px',
                                    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                                    border: 'none'
                                }}>
                                    <div className="card-body">
                                        <h5 className="card-title" style={{ color: '#2e7d32', marginBottom: '15px', fontWeight: '600' }}>Contractors</h5>
                                        {Object.entries(contractors).map(([key, value]) => (
                                            <div className="d-flex justify-content-between mb-2" key={key}>
                                                <span>{key}:</span>
                                                <strong>{value}</strong>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Safety Activities Section */}
                            <div className="col-md-6 mb-3">
                                <div className="card h-100" style={{
                                    backgroundColor: '#fff8e1',
                                    borderRadius: '8px',
                                    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                                    border: 'none'
                                }}>
                                    <div className="card-body">
                                        <h5 className="card-title" style={{ color: '#f57f17', marginBottom: '15px', fontWeight: '600' }}>Safety Activities</h5>
                                        {Object.entries(safetyActivities).map(([key, value]) => (
                                            <div className="d-flex justify-content-between mb-2" key={key}>
                                                <span>{key}:</span>
                                                <strong>{value}</strong>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* EHS Activities Section */}
                            <div className="col-md-6 mb-3">
                                <div className="card h-100" style={{
                                    backgroundColor: '#f3e5f5',
                                    borderRadius: '8px',
                                    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                                    border: 'none'
                                }}>
                                    <div className="card-body">
                                        <h5 className="card-title" style={{ color: '#8e24aa', marginBottom: '15px', fontWeight: '600' }}>EHS Activities</h5>
                                        {Object.entries(ehsActivities).map(([key, value]) => (
                                            <div className="d-flex justify-content-between mb-2" key={key}>
                                                <span>{key}:</span>
                                                <strong>{value}</strong>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* External Section */}
                            <div className="col-md-6 mb-3">
                                <div className="card h-100" style={{
                                    backgroundColor: '#fce4ec',
                                    borderRadius: '8px',
                                    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                                    border: 'none'
                                }}>
                                    <div className="card-body">
                                        <h5 className="card-title" style={{ color: '#c2185b', marginBottom: '15px', fontWeight: '600' }}>External</h5>
                                        {Object.entries(external).map(([key, value]) => (
                                            <div className="d-flex justify-content-between mb-2" key={key}>
                                                <span>{key}:</span>
                                                <strong>{value}</strong>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer style={{ borderTop: 'none' }}>
                    <Button
                        variant="secondary"
                        onClick={() => setShowReportModal(false)}
                        style={{
                            backgroundColor: '#f5f5f5',
                            color: '#333',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '8px 20px'
                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    };

    return (
        <div className="monthly-report-container">



            <div className="d-flex mb-4 align-items-center">
                <div className="input-group me-2" style={{ flex: 1 }}>
                    <input
                        type="text"
                        className="form-control"
                        placeholder="Search locations..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                <div className="me-2" style={{ minWidth: '200px' }}>
                    <select
                        className="form-select"
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                    >
                        <option value="">All Status</option>
                        <option value="approved">Approved</option>
                        <option value="submitted">Submitted</option>
                        <option value="no-submission-yellow">No Submission (Pending)</option>
                        <option value="no-submission-blue">No Submission (Inactive)</option>
                        <option value="no-assigned">No Assigned Reporter/Reviewer</option>
                    </select>
                </div>

                <button className="btn btn-outline-primary" style={{ display: 'flex', alignItems: 'center', gap: '5px' }} onClick={() => exportYearWiseExcelWithProjectName(data, `Statistics_${selectedYear}.xlsx`)}>
                    <i className="pi pi-file-excel"></i> Export Excel
                </button>

            </div>

            <div className="calendar-grid">
                <div className="calendar-header">
                    <div className="location-header">Location</div>
                    {months.map(month => (
                        <div key={month} className="month-header">
                            {month} {selectedYear.slice(-2)}
                        </div>
                    ))}
                </div>

                <div className="calendar-body">
                    {filteredData.length > 0 ? (
                        filteredData.map((location, index) => (
                            <div key={index} className="calendar-row">
                                <div className="location-cell">
                                    {location.name}
                                </div>
                                {months.map(month => (
                                    <div key={month} className="month-cell-container">
                                        {renderMonthCell(location, month)}
                                    </div>
                                ))}
                            </div>
                        ))
                    ) : (
                        <div className="no-data-message">
                            <p>No locations found matching your search criteria.</p>
                        </div>
                    )}
                </div>
            </div>

            <ReportDetailModal />

            <style jsx="true">{`
                .calendar-grid {
                    display: flex;
                    flex-direction: column;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    overflow: hidden;
                    width: 100%;
                    table-layout: fixed;
                }

                .calendar-header {
                    display: flex;
                    background-color: #2962ff;
                    font-weight: bold;
                    color: white;
                }

                .location-header {
                    width: 250px;
                    padding: 10px;
                    border-right: 1px solid rgba(255, 255, 255, 0.2);
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                }

                .month-header {
                    width: calc((100% - 250px) / 12);
                    min-width: 80px;
                    padding: 10px;
                    text-align: center;
                    border-right: 1px solid rgba(255, 255, 255, 0.2);
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    white-space: nowrap;
                }

                .calendar-body {
                    display: flex;
                    flex-direction: column;
                }

                .calendar-row {
                    display: flex;
                    border-bottom: 1px solid #ddd;
                }

                .location-cell {
                    width: 250px;
                    padding: 10px;
                    border-right: 1px solid #ddd;
                    background-color: #f8f9fa;
                    font-size: 0.9rem;
                    color: #3f51b5;
                }

                .month-cell-container {
                    width: calc((100% - 250px) / 12);
                    min-width: 80px;
                    border-right: 1px solid #ddd;
                    min-height: 80px;
                }

                .month-cell {
                    border-radius: 4px;
                    margin: 2px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    font-size: 0.85rem;
                }

                .view-btn {
                    font-size: 0.8rem;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .view-btn:hover {
                    background-color: white !important;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }

                .monthly-report-container {
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                }


                .no-data-message {
                    padding: 30px;
                    text-align: center;
                    color: #666;
                    font-size: 1.1rem;
                    background-color: #f9f9f9;
                    border-radius: 4px;
                    width: 100%;
                }
            `}</style>
        </div>
    );
}

export default MonthlyReport;
