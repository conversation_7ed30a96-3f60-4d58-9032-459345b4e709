import React, { useState, useEffect, useRef, useCallback } from "react";
import moment from "moment";
import "./InspectionReport.css";
import { STATIC_URL, USERS_URL } from '../../constants';
import GalleryPage from '../../apps/Gallery';
import API from '../../services/API';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

/**
 * InspectionReport – Custom styled printable report matching report.html design
 * @param {Object} props.inspection – inspection JSON object
 */
export default function InspectionReport({ inspection, totalActions }) {
    console.log(inspection, 'inspection')
    console.log(totalActions, 'totalActions')

  /* -------------------- state -------------------- */
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const reportRef = useRef();

  /* -------------------- effects -------------------- */
  useEffect(() => {
    getAllUsers();
  }, []);



  /* -------------------- API calls -------------------- */
  const getAllUsers = async () => {
    try {
      setUsersLoading(true);
      const response = await API.get(USERS_URL);
      setUsers(response.data);
    } catch (error) {
      console.error('Error fetching users:', error);
      setUsers([]);
    } finally {
      setUsersLoading(false);
    }
  };

  /* -------------------- helpers -------------------- */
  const formatDate = (d) => {
    if (!d) return "-";
    const date = new Date(d);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  const formatDateTime = (d) => {
    if (!d) return "-";
    const date = new Date(d);
    return (
      date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      }) +
      ", " +
      date.toLocaleTimeString("en-GB", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  // Helper function to clean HTML tags and entities from text
  const cleanHtmlText = (text) => {
    if (!text) return "";
    return text
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .replace(/&nbsp;/g, " ") // Replace &nbsp; with space
      .replace(/&amp;/g, "&") // Replace &amp; with &
      .replace(/&lt;/g, "<") // Replace &lt; with <
      .replace(/&gt;/g, ">") // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#39;/g, "'") // Replace &#39; with '
      .replace(/&#x27;/g, "'") // Replace &#x27; with '
      .replace(/&#x2F;/g, "/") // Replace &#x2F; with /
      .trim(); // Remove leading/trailing whitespace
  };

  /* ---------------- derived data ------------------- */
  const included = [];
  const notIncluded = [];
  inspection?.checklistReport?.forEach((grp) => {
    if (grp.type === "checklist-group") {
      // Clean HTML tags and entities from area labels
      const cleanLabel = cleanHtmlText(grp.label);
      (grp.checked ? included : notIncluded).push(cleanLabel);
    }
  });

  // Filter to show only checked/included checklist groups
  const checkedGroups = inspection?.checklistReport?.filter((grp) =>
    grp.type === "checklist-group" && grp.checked
  ) || [];

  const completedCount =
    inspection?.inspectionData?.completedActions?.length || 0;

  const statusVariant = /done|completed/i.test(inspection.status)
    ? "success"
    : /open/i.test(inspection.status)
    ? "warning"
    : "secondary";

  // Process actions similar to ActionTable.js
  const processedActions = React.useMemo(() => {
    let cmCounter = 0.0;
    return (totalActions || [])
      .map(action => {
        if (action.firstActionType === 'ins_take_actions_control') {
          cmCounter += 1.0;
          return { ...action, cm: cmCounter };
        }
        return null;
      })
      .filter(action => action !== null);
  }, [totalActions]);

  // Helper functions similar to ActionTable.js
  const getActionNumber = (action) => {
    let status = '';
    action.data.forEach(item => {
      if (item.actionType === 'ins_take_actions_control') {
        status = `IA-${action.cm.toFixed(1)}`;
      } else if (item.actionType === 'ins_retake_actions') {
        status = `IA-${(action.cm + 0.1).toFixed(1)}`;
      }
    });
    return status;
  };

  const getActionStatus = (action) => {
    switch (action.lastActionType) {
      case 'ins_take_actions_control':
        return 'Assigned';
      case 'ins_retake_actions':
        return 'Re-Assigned';
      case 'ins_verify_actions':
        return 'Implemented - Pending Verification';
      case 'approve':
        return 'Verified & Closed';
      default:
        return 'Open';
    }
  };

  // Helper function to get user name by ID (similar to ActionTable.js)
  const getName = (id) => {
    if (!id) return 'N/A';
    if (usersLoading) return 'Loading...';
    const user = users.find(user => user.id === id);
    return user?.firstName || `User ID: ${id}`;
  };

  const getActionAssignee = (action, item) => {
    // Use the assignedToId from the specific item, not the action level
    const assigneeId = item?.assignedToId || action.lastAssignedToId;
    const assigneeName = item?.assignedToName || action.lastAssignedToName || getName(assigneeId);

    switch (action.lastActionType) {
      case "ins_take_actions_control":
      case "take_actions_ra":
      case "audit_take_actions":
      case "take_actions_control_post":
        return `Action Assignee: ${assigneeName}`;
      case "ins_verify_actions":
        return `Action to be verified by: ${assigneeName}`;
      case "ins_retake_actions":
        return `Action Re-assigned to: ${assigneeName}`;
      case "approve":
        return `Action Verified By: ${assigneeName}`;
      default:
        return '';
    }
  };

  // PDF Export function
  const exportToPDF = useCallback(async () => {
    try {
      setIsExporting(true);

      const element = reportRef.current;
      if (!element) {
        alert('Report element not found. Please try again.');
        return;
      }

      // Wait a bit for images to load if they're still loading
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Configure html2canvas options for better quality
      const canvas = await html2canvas(element, {
        scale: 2, // Higher resolution for better quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        width: element.scrollWidth,
        height: element.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        onclone: (clonedDoc) => {
          // Ensure all images are loaded in the cloned document
          const images = clonedDoc.querySelectorAll('img');
          images.forEach(img => {
            if (img.src.startsWith('blob:')) {
              img.style.display = 'none';
            }
          });
        }
      });

      const imgData = canvas.toDataURL('image/png', 0.95);

      // Create PDF with A4 dimensions
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const margin = 10; // 10mm margin

      // Calculate image dimensions to fit A4 with margins
      const availableWidth = pdfWidth - (margin * 2);
      const availableHeight = pdfHeight - (margin * 2);

      const imgWidth = availableWidth;
      const imgHeight = (canvas.height * availableWidth) / canvas.width;

      let heightLeft = imgHeight;
      let position = margin;

      // Add first page
      pdf.addImage(imgData, 'PNG', margin, position, imgWidth, imgHeight);
      heightLeft -= availableHeight;

      // Add additional pages if content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight + margin;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', margin, position, imgWidth, imgHeight);
        heightLeft -= availableHeight;
      }

      // Save the PDF with a descriptive filename
      const fileName = `Inspection_Report_${inspection.maskId}_${moment().format('YYYY-MM-DD_HH-mm')}.pdf`;
      pdf.save(fileName);

      // Show success message
      alert(`PDF exported successfully as "${fileName}"`);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please check your internet connection and try again.');
    } finally {
      setIsExporting(false);
    }
  }, [inspection.maskId]);

  // Add keyboard shortcut for PDF export (Ctrl+P or Cmd+P)
  useEffect(() => {
    const handleKeyDown = (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
        event.preventDefault();
        if (!isExporting) {
          exportToPDF();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isExporting, exportToPDF]);

  // Helper function to render file uploads (updated for PDF export)
  const renderUploads = (uploads, title = "Images") => {
    if (!uploads || uploads.length === 0) return null;

    return (
      <div className="inspection-report-uploads-section">
        <div className="inspection-report-uploads-title">{title}</div>
        <div className="inspection-report-uploads-grid">
          {uploads.map((upload, index) => {
            const fileExtension = upload.split('.').pop().toLowerCase();

            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
              // For PDF export, show image directly instead of GalleryPage
              if (isExporting) {
                return (
                  <div key={index} className="inspection-report-upload-item">
                    <img
                      src={`${STATIC_URL}/${upload}`}
                      alt={`Upload ${index + 1}`}
                      className="inspection-report-export-image"
                      crossOrigin="anonymous"
                    />
                  </div>
                );
              } else {
                // Normal view with GalleryPage
                return (
                  <div key={index} className="inspection-report-upload-item">
                    <GalleryPage
                      photos={[{
                        src: `${STATIC_URL}/${upload}`,
                        width: 4,
                        height: 3
                      }]}
                    />
                  </div>
                );
              }
            } else if (fileExtension === 'pdf') {
              // Handle PDF files
              return (
                <div key={index} className="inspection-report-upload-item">
                  <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer" className="inspection-report-file-link">
                    📄 View PDF
                  </a>
                </div>
              );
            } else if (['xls', 'xlsx'].includes(fileExtension)) {
              // Handle Excel files
              return (
                <div key={index} className="inspection-report-upload-item">
                  <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer" className="inspection-report-file-link">
                    📊 Download Excel File
                  </a>
                </div>
              );
            } else {
              // Handle other file types
              return (
                <div key={index} className="inspection-report-upload-item">
                  <span className="inspection-report-unsupported-file">
                    📎 Unsupported file type: {fileExtension}
                  </span>
                </div>
              );
            }
          })}
        </div>
      </div>
    );
  };

  // Match actions with checklist questions similar to ActionTable.js
  const matchedResults = [];
  processedActions.forEach(action => {
    const actionToBeTaken = action.data?.[0]?.actionToBeTaken;
    if (actionToBeTaken) {
      const postMatch = inspection.postActions?.find(
        p => p.actionToBeTaken === actionToBeTaken
      );
      if (postMatch) {
        let label = '';
        let main = '';
        inspection.checklist?.value?.forEach((group, groupIndex) => {
          if (postMatch.index === groupIndex) {
            if (group.type === "checklist-group" && Array.isArray(group.questions)) {
              group.questions.forEach((question, questionIndex) => {
                if (postMatch.i === questionIndex) {
                  label = cleanHtmlText(question.label);
                  main = cleanHtmlText(group.label);
                }
              });
            }
          }
        });
        matchedResults.push({
          actionToBeTaken,
          label,
          main
        });
      }
    }
  });

  /* ------------------- render ---------------------- */
  // Show loading indicator while users are being fetched
  if (usersLoading) {
    return (
      <div className="inspection-report-container">
        <div className="inspection-report-page-container">
          <div className="inspection-report-loading">
            <div className="inspection-report-loading-text">Loading user data...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="inspection-report-container">
      {/* PDF Export Button */}
      <div className="inspection-report-export-controls">
        <button
          onClick={exportToPDF}
          disabled={isExporting}
          className="inspection-report-export-btn"
        >
          {isExporting ? (
            <>
              <span className="inspection-report-export-spinner"></span>
              Generating PDF...
            </>
          ) : (
            <>
              📄 Export to PDF
            </>
          )}
        </button>
      </div>

      <div className="inspection-report-page-container" ref={reportRef}>
      {/* Header Container */}
      <div className="inspection-report-header-container">
        <div className="inspection-report-header-title">Inspection Report</div>
        <div className={`inspection-report-status-badge ${statusVariant === 'success' ? 'completed' : statusVariant === 'warning' ? 'pending' : 'in-progress'}`}>
          {inspection.status}
        </div>
        <div className="inspection-report-timestamp">{formatDateTime(inspection.created)}</div>
      </div>

      {/* Header Section */}
      <div className="inspection-report-header-section">
        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Checklist Name</div>
          <div className="inspection-report-header-value">{inspection?.checklist?.name || "-"}</div>
        </div>
        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Version</div>
          <div className="inspection-report-header-value">{inspection?.checklist?.version || "-"}</div>
        </div>
        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Reference #</div>
          <div className="inspection-report-header-value">{inspection.maskId}</div>
        </div>

        <div className="inspection-report-header-block full-width">
          <div className="inspection-report-header-label">Location</div>
          <div className="inspection-report-header-value">
            {[
              inspection.locationOne?.name,
              inspection.locationTwo?.name,
              inspection.locationThree?.name,
              inspection.locationFour?.name,
            ]
              .filter(Boolean)
              .join(" - ")}
          </div>
        </div>

        <div className="inspection-report-header-block"></div>

        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Date of Inspection</div>
          <div className="inspection-report-header-value">{inspection.dateTime ? moment(inspection.dateTime, "DD-MM-YYYY").format("Do MMM YYYY") : "-"}</div>
        </div>

        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Assigned Date</div>
          <div className="inspection-report-header-value">{formatDate(inspection.created)}</div>
        </div>

        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Start Date</div>
          <div className="inspection-report-header-value">{inspection.startDateTime ? moment(inspection.startDateTime, "DD-MM-YYYY").format("Do MMM YYYY") : "-"}</div>
        </div>

        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Due Date</div>
          <div className="inspection-report-header-value">{inspection.dateTime ? moment(inspection.dateTime, "DD-MM-YYYY").format("Do MMM YYYY") : "-"}</div>
        </div>

        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Assigned to</div>
          <div className="inspection-report-header-value">{inspection.assignedTo?.firstName || "-"}</div>
        </div>

        <div className="inspection-report-header-block">
          <div className="inspection-report-header-label">Assignee</div>
          <div className="inspection-report-header-value">{inspection.assignedBy?.firstName || "-"}</div>
        </div>
      </div>

      {/* Areas Container - Show both included and not included areas */}
      <div className="inspection-report-areas-container">
        <div className="inspection-report-areas-section">
          <div className="inspection-report-areas-header included">Areas Included</div>
          <ul className="inspection-report-areas-list included">
            {included.length ? (
              included.map((a) => (
                <li key={a}>{a}</li>
              ))
            ) : (
              <li>No areas included</li>
            )}
          </ul>
        </div>

        <div className="inspection-report-areas-section">
          <div className="inspection-report-areas-header not-included">Areas Not Included</div>
          <ul className="inspection-report-areas-list not-included">
            {notIncluded.length ? (
              notIncluded.map((a) => (
                <li key={a}>{a}</li>
              ))
            ) : (
              <li>No areas excluded</li>
            )}
          </ul>
        </div>
      </div>

      {/* Inspection Checklist - Show only checked groups */}
      {checkedGroups.length > 0 ? (
        <>
          <div className="inspection-report-section-title">Inspection Checklist</div>

          {checkedGroups.map((grp, idx) => {
            // Clean HTML tags and entities from group label
            const groupLabel = cleanHtmlText(grp.label);

            return (
              <div key={grp.name}>
                <div className="inspection-report-tabs">
                  <div className="inspection-report-tab green">{idx + 1}. {groupLabel}</div>
                </div>

                {grp.questions?.map((q) => {
                  const selected = q.options?.find((o) => o.checked === 1);
                  const answer = selected?.label || "N/A";
                  const btnClass = answer === "Yes" ? "yes" : answer === "No" ? "no" : "na";
                  const answerSymbol = answer === "Yes" ? "✓" : answer === "No" ? "✕" : "";
                  const cleanQuestionLabel = cleanHtmlText(q.label);

                  return (
                    <div key={q.name} className="inspection-report-checklist-item">
                      <div className="inspection-report-checklist-text">{cleanQuestionLabel}</div>
                      <div className="inspection-report-response">
                        <span className={`inspection-report-btn ${btnClass}`}>
                          {answerSymbol} {answer}
                        </span>
                      </div>
                      {q.remarks && (
                        <>
                          <div className="inspection-report-remark-label">Remarks</div>
                          <div className="inspection-report-remark-box">{q.remarks}</div>
                        </>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </>
      ) : (
        <div className="inspection-report-section-title">
          No checklist groups were checked/included in this inspection
        </div>
      )}

      {/* Actions - Using totalActions structure */}
      {processedActions?.length ? (
        <>
          <div className="inspection-report-section-title">{inspection.maskId} Actions</div>
          {processedActions.map((action, index) => {
            const matched = matchedResults[index];
            const actionNumber = getActionNumber(action);
            const actionStatus = getActionStatus(action);

            return (
              <div key={index} className="inspection-report-action-container">
                {/* Action Header */}
                <div className="inspection-report-action-header">
                  <div className="inspection-report-action-header-left">
                    <div className="inspection-report-action-id">
                      {inspection.maskId} - {actionNumber}
                    </div>
                  </div>
                  <div className="inspection-report-action-header-right">
                    <span className={`inspection-report-action-badge ${
                      actionStatus === 'Verified & Closed' ? 'completed' :
                      actionStatus === 'Assigned' ? 'assigned' :
                      actionStatus === 'Re-Assigned' ? 'reassigned' : 'pending'
                    }`}>
                      {actionStatus}
                    </span>
                  </div>
                </div>

                {/* Related Question */}
                {matched && (
                  <div className="inspection-report-action-question">
                    <div className="inspection-report-action-question-group">
                      {matched.main}
                    </div>
                    <div className="inspection-report-action-question-text">
                      {matched.label}
                    </div>
                  </div>
                )}

                {/* Action Details */}
                {action.data?.map((item, itemIndex) => (
                  <div key={itemIndex} className="inspection-report-action-detail">
                    {item.actionType === 'ins_take_actions_control' && (
                      <>
                        {/* Show uploads from postActions if action is completed */}
                        {action.firstActionType !== action.lastActionType && inspection.postActions && (
                          <>
                            {inspection.postActions.find(postAction => postAction.actionToBeTaken === item.actionToBeTaken)?.uploads &&
                              renderUploads(
                                inspection.postActions.find(postAction => postAction.actionToBeTaken === item.actionToBeTaken)?.uploads,
                                "Images"
                              )
                            }
                          </>
                        )}

                        <div className="inspection-report-action-section">
                          <div className="inspection-report-action-title">
                            Assigned Action - {actionNumber}
                          </div>
                          <div className="inspection-report-action-content">
                            {item.actionToBeTaken}
                          </div>
                        </div>

                        {item.status === 'open' && (
                          <div className="inspection-report-action-section">
                            <div className="inspection-report-action-title">Action Assignee</div>
                            <div className="inspection-report-action-content">
                              {item.assignedToName || getName(item.assignedToId)}
                            </div>
                          </div>
                        )}

                        {item.status === 'submitted' && (
                          <>
                            <div className="inspection-report-action-section">
                              <div className="inspection-report-action-title">Action Taken</div>
                              <div className="inspection-report-action-content">
                                {item.actionTaken}
                              </div>
                            </div>

                            <div className="inspection-report-action-meta-row">
                              <div className="inspection-report-action-meta-col">
                                <div className="inspection-report-action-label">Action Taken By</div>
                                <div className="inspection-report-action-value">
                                  {item.assignedToName || getName(item.assignedToId)}
                                </div>
                              </div>
                              <div className="inspection-report-action-meta-col">
                                <div className="inspection-report-action-label">Date</div>
                                <div className="inspection-report-action-value">
                                  {item.createdDate ? moment(item.createdDate).format('Do MMM YYYY, hh:mm A') : '-'}
                                </div>
                              </div>
                            </div>

                            <div className="inspection-report-action-meta-row">
                              <div className="inspection-report-action-meta-col">
                                <div className="inspection-report-action-label">Due Date</div>
                                <div className="inspection-report-action-value">
                                  {formatDate(item.dueDate)}
                                </div>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Show uploads from the action item itself */}
                        {item.uploads && renderUploads(
                          item.uploads,
                          action.firstActionType === action.lastActionType ? 'Images' : 'Evidence'
                        )}
                      </>
                    )}

                    {item.actionType === 'ins_retake_actions' && (
                      <>
                        <div className="inspection-report-action-section">
                          <div className="inspection-report-action-title">
                            Action Verifier Comments & Reassigned Action - {getActionNumber({...action, cm: action.cm + 0.1})}
                          </div>
                          <div className="inspection-report-action-content">
                            {item.actionToBeTaken}
                          </div>
                        </div>

                        {item.comments && (
                          <div className="inspection-report-action-section">
                            <div className="inspection-report-action-title">Comments</div>
                            <div className="inspection-report-action-content">
                              {item.comments}
                            </div>
                          </div>
                        )}

                        {item.status !== 'open' && item.actionTaken && (
                          <div className="inspection-report-action-section">
                            <div className="inspection-report-action-title">Action Taken</div>
                            <div className="inspection-report-action-content">
                              {item.actionTaken}
                            </div>
                          </div>
                        )}

                        <div className="inspection-report-action-meta-row">
                          <div className="inspection-report-action-meta-col">
                            <div className="inspection-report-action-label">
                              {item.status === 'open' ? 'Action Assignee' : 'Action Taken By'}
                            </div>
                            <div className="inspection-report-action-value">
                              {item.assignedToName || getName(item.assignedToId)}
                            </div>
                          </div>
                          <div className="inspection-report-action-meta-col">
                            <div className="inspection-report-action-label">Date</div>
                            <div className="inspection-report-action-value">
                              {item.createdDate ? moment(item.createdDate).format('Do MMM YYYY, hh:mm A') : '-'}
                            </div>
                          </div>
                        </div>

                        <div className="inspection-report-action-meta-row">
                          <div className="inspection-report-action-meta-col">
                            <div className="inspection-report-action-label">Due Date</div>
                            <div className="inspection-report-action-value">
                              {formatDate(item.dueDate)}
                            </div>
                          </div>
                        </div>

                        {/* Show uploads for retake actions */}
                        {item.uploads && renderUploads(item.uploads, "Images")}
                      </>
                    )}

                    {item.actionType === 'ins_verify_actions' && (
                      <div className="inspection-report-action-section">
                        <div className="inspection-report-action-meta-row">
                          <div className="inspection-report-action-meta-col">
                            <div className="inspection-report-action-label">Action Verifier</div>
                            <div className="inspection-report-action-value">
                              {item.assignedToName || getName(item.assignedToId)}
                            </div>
                          </div>
                          {item.status === 'submitted' && (
                            <div className="inspection-report-action-meta-col">
                              <div className="inspection-report-action-label">Date</div>
                              <div className="inspection-report-action-value">
                                {item.createdDate ? moment(item.createdDate).format('Do MMM YYYY, hh:mm A') : '-'}
                              </div>
                            </div>
                          )}
                        </div>
                        {item.status === 'submitted' && item.comments && (
                          <div className="inspection-report-action-section">
                            <div className="inspection-report-action-title">Action Verifier Comments</div>
                            <div className="inspection-report-action-content">
                              {item.comments}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {item.actionType === 'approve' && item.status === 'submitted' && (
                      <>
                        <div className="inspection-report-action-meta-row">
                          <div className="inspection-report-action-meta-col">
                            <div className="inspection-report-action-label">Action Verified By</div>
                            <div className="inspection-report-action-value">
                              {item.assignedToName || getName(item.assignedToId)}
                            </div>
                          </div>
                          <div className="inspection-report-action-meta-col">
                            <div className="inspection-report-action-label">Date</div>
                            <div className="inspection-report-action-value">
                              {item.createdDate ? moment(item.createdDate).format('Do MMM YYYY, hh:mm A') : '-'}
                            </div>
                          </div>
                        </div>
                        {item.comments && (
                          <div className="inspection-report-action-section">
                            <div className="inspection-report-action-title">Action Verifier Comments</div>
                            <div className="inspection-report-action-content">
                              {item.comments}
                            </div>
                          </div>
                        )}
                      </>
                    )}

                    {item.actionType === 'reject' && item.status === 'submitted' && (
                      <div className="inspection-report-action-meta-row">
                        <div className="inspection-report-action-meta-col">
                          <div className="inspection-report-action-label">Action Verified By</div>
                          <div className="inspection-report-action-value">
                            {item.assignedToName || getName(item.assignedToId)}
                          </div>
                        </div>
                        <div className="inspection-report-action-meta-col">
                          <div className="inspection-report-action-label">Date</div>
                          <div className="inspection-report-action-value">
                            {item.createdDate ? moment(item.createdDate).format('Do MMM YYYY, hh:mm A') : '-'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            );
          })}
        </>
      ) : null}
      </div>
    </div>
  );
}
